import { CourseGenerationRequest } from '../types/course';

export interface PrerequisiteInfo {
  subject: string;
  level: 'basic' | 'intermediate' | 'advanced';
  description: string;
}

export interface IndustryAlignment {
  skills: string[];
  tools: string[];
  certifications: string[];
  careerPaths: string[];
  industryTrends: string[];
}

export interface CourseContext {
  prerequisites: PrerequisiteInfo[];
  industryAlignment: IndustryAlignment;
  progressionPath: string[];
  difficultyLevel: number; // 1-10 scale
  marketDemand: 'high' | 'medium' | 'low';
  emergingTrends: string[];
}

// Knowledge base for different subjects and their contexts
const subjectContextDatabase: Record<string, Partial<CourseContext>> = {
  // Computer Science subjects
  'data structures': {
    prerequisites: [
      { subject: 'Programming Fundamentals', level: 'basic', description: 'Basic programming concepts in any language' },
      { subject: 'Mathematics', level: 'basic', description: 'Discrete mathematics and basic algebra' }
    ],
    industryAlignment: {
      skills: ['Algorithm Design', 'Problem Solving', 'Code Optimization', 'Memory Management'],
      tools: ['IDE', 'Debuggers', 'Profiling Tools', 'Version Control'],
      certifications: ['Oracle Certified Professional', 'Microsoft Certified Developer'],
      careerPaths: ['Software Developer', 'System Architect', 'Technical Lead'],
      industryTrends: ['Big Data Processing', 'Real-time Systems', 'Cloud Computing']
    },
    progressionPath: ['Programming Fundamentals', 'Data Structures', 'Algorithms', 'System Design'],
    difficultyLevel: 6,
    marketDemand: 'high'
  },

  'machine learning': {
    prerequisites: [
      { subject: 'Statistics and Probability', level: 'intermediate', description: 'Statistical analysis and probability theory' },
      { subject: 'Linear Algebra', level: 'intermediate', description: 'Matrix operations and vector spaces' },
      { subject: 'Programming', level: 'intermediate', description: 'Python or R programming' }
    ],
    industryAlignment: {
      skills: ['Data Analysis', 'Model Development', 'Feature Engineering', 'Model Evaluation'],
      tools: ['Python', 'TensorFlow', 'PyTorch', 'Scikit-learn', 'Jupyter', 'Git'],
      certifications: ['Google Cloud ML Engineer', 'AWS ML Specialty', 'Microsoft Azure AI Engineer'],
      careerPaths: ['ML Engineer', 'Data Scientist', 'AI Researcher', 'ML Architect'],
      industryTrends: ['Large Language Models', 'AutoML', 'Edge AI', 'Explainable AI']
    },
    progressionPath: ['Statistics', 'Programming', 'Machine Learning', 'Deep Learning', 'AI Systems'],
    difficultyLevel: 8,
    marketDemand: 'high',
    emergingTrends: ['Generative AI', 'MLOps', 'Federated Learning', 'Quantum ML']
  },

  'database': {
    prerequisites: [
      { subject: 'Programming Fundamentals', level: 'basic', description: 'Basic programming and logic' },
      { subject: 'Data Structures', level: 'basic', description: 'Understanding of data organization' }
    ],
    industryAlignment: {
      skills: ['Database Design', 'Query Optimization', 'Data Modeling', 'Performance Tuning'],
      tools: ['MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQL Server', 'Oracle'],
      certifications: ['Oracle Database Administrator', 'Microsoft SQL Server', 'MongoDB Certified'],
      careerPaths: ['Database Administrator', 'Data Engineer', 'Backend Developer'],
      industryTrends: ['NoSQL Databases', 'Cloud Databases', 'Data Lakes', 'Real-time Analytics']
    },
    progressionPath: ['Data Fundamentals', 'Relational Databases', 'Advanced Databases', 'Big Data'],
    difficultyLevel: 5,
    marketDemand: 'high'
  },

  // Engineering subjects
  'thermodynamics': {
    prerequisites: [
      { subject: 'Physics', level: 'intermediate', description: 'Heat, work, and energy concepts' },
      { subject: 'Calculus', level: 'intermediate', description: 'Differential and integral calculus' },
      { subject: 'Chemistry', level: 'basic', description: 'Basic chemical principles' }
    ],
    industryAlignment: {
      skills: ['Energy Analysis', 'System Design', 'Process Optimization', 'Heat Transfer'],
      tools: ['MATLAB', 'ANSYS', 'Aspen HYSYS', 'EES', 'Thermodynamic Tables'],
      certifications: ['Professional Engineer (PE)', 'Energy Manager Certification'],
      careerPaths: ['Mechanical Engineer', 'Energy Engineer', 'Process Engineer', 'HVAC Engineer'],
      industryTrends: ['Renewable Energy', 'Energy Storage', 'Heat Pumps', 'Sustainable Design']
    },
    progressionPath: ['Physics', 'Thermodynamics', 'Heat Transfer', 'Energy Systems'],
    difficultyLevel: 7,
    marketDemand: 'medium'
  },

  'circuits': {
    prerequisites: [
      { subject: 'Physics', level: 'intermediate', description: 'Electricity and magnetism' },
      { subject: 'Calculus', level: 'intermediate', description: 'Differential equations' },
      { subject: 'Linear Algebra', level: 'basic', description: 'Matrix operations' }
    ],
    industryAlignment: {
      skills: ['Circuit Analysis', 'PCB Design', 'Signal Processing', 'Electronic Design'],
      tools: ['SPICE', 'Multisim', 'Altium Designer', 'KiCad', 'Oscilloscope', 'Function Generator'],
      certifications: ['IPC Certification', 'Certified Electronics Technician'],
      careerPaths: ['Electronics Engineer', 'Circuit Designer', 'Hardware Engineer', 'Test Engineer'],
      industryTrends: ['IoT Devices', 'Wearable Electronics', 'Power Electronics', 'RF Design']
    },
    progressionPath: ['Basic Electronics', 'Circuit Analysis', 'Electronic Devices', 'System Design'],
    difficultyLevel: 6,
    marketDemand: 'medium'
  }
};

export class CourseContextService {
  static analyzePrerequisites(subject: string, level: string, semester: number): PrerequisiteInfo[] {
    const subjectKey = subject.toLowerCase();
    const contextData = this.findBestMatch(subjectKey);
    
    if (contextData?.prerequisites) {
      return contextData.prerequisites;
    }

    // Generate generic prerequisites based on level and semester
    const prerequisites: PrerequisiteInfo[] = [];
    
    if (level === 'undergraduate') {
      if (semester > 2) {
        prerequisites.push({
          subject: 'Mathematics',
          level: 'basic',
          description: 'Basic mathematical concepts and problem-solving skills'
        });
      }
      if (semester > 4) {
        prerequisites.push({
          subject: 'Core Foundation Courses',
          level: 'intermediate',
          description: 'Fundamental courses in the chosen field of study'
        });
      }
    } else {
      prerequisites.push({
        subject: 'Undergraduate Degree',
        level: 'advanced',
        description: 'Bachelor\'s degree in relevant field with good academic standing'
      });
    }

    return prerequisites;
  }

  static getIndustryAlignment(subject: string, department: string): IndustryAlignment {
    const subjectKey = subject.toLowerCase();
    const contextData = this.findBestMatch(subjectKey);
    
    if (contextData?.industryAlignment) {
      return contextData.industryAlignment;
    }

    // Generate generic industry alignment based on department
    return {
      skills: ['Problem Solving', 'Analytical Thinking', 'Communication', 'Teamwork'],
      tools: ['Microsoft Office', 'Project Management Tools', 'Industry-specific Software'],
      certifications: ['Professional Certification in ' + department],
      careerPaths: ['Specialist', 'Consultant', 'Manager', 'Researcher'],
      industryTrends: ['Digital Transformation', 'Automation', 'Sustainability', 'Innovation']
    };
  }

  static generateProgressionPath(subject: string, level: string): string[] {
    const subjectKey = subject.toLowerCase();
    const contextData = this.findBestMatch(subjectKey);
    
    if (contextData?.progressionPath) {
      return contextData.progressionPath;
    }

    // Generate generic progression path
    if (level === 'undergraduate') {
      return [
        'Foundation Concepts',
        subject,
        'Advanced ' + subject,
        'Specialized Applications'
      ];
    } else {
      return [
        'Advanced Theory',
        'Research Methodology',
        subject,
        'Thesis/Project Work'
      ];
    }
  }

  static assessMarketDemand(subject: string): 'high' | 'medium' | 'low' {
    const subjectKey = subject.toLowerCase();
    const contextData = this.findBestMatch(subjectKey);
    
    if (contextData?.marketDemand) {
      return contextData.marketDemand;
    }

    // Assess based on current trends
    const highDemandKeywords = ['machine learning', 'ai', 'data', 'cyber', 'cloud', 'mobile'];
    const mediumDemandKeywords = ['web', 'software', 'network', 'database', 'system'];
    
    if (highDemandKeywords.some(keyword => subjectKey.includes(keyword))) {
      return 'high';
    } else if (mediumDemandKeywords.some(keyword => subjectKey.includes(keyword))) {
      return 'medium';
    }
    
    return 'medium';
  }

  static getEmergingTrends(subject: string): string[] {
    const subjectKey = subject.toLowerCase();
    const contextData = this.findBestMatch(subjectKey);
    
    if (contextData?.emergingTrends) {
      return contextData.emergingTrends;
    }

    // Generate generic emerging trends
    return [
      'Digital Transformation',
      'Artificial Intelligence Integration',
      'Sustainability Focus',
      'Remote Collaboration Tools'
    ];
  }

  static generateCourseContext(request: CourseGenerationRequest): CourseContext {
    return {
      prerequisites: this.analyzePrerequisites(request.subject, request.level, request.semester),
      industryAlignment: this.getIndustryAlignment(request.subject, request.department),
      progressionPath: this.generateProgressionPath(request.subject, request.level),
      difficultyLevel: this.assessDifficultyLevel(request.subject, request.level, request.semester),
      marketDemand: this.assessMarketDemand(request.subject),
      emergingTrends: this.getEmergingTrends(request.subject)
    };
  }

  private static findBestMatch(subjectKey: string): Partial<CourseContext> | null {
    // Direct match
    if (subjectContextDatabase[subjectKey]) {
      return subjectContextDatabase[subjectKey];
    }

    // Partial match
    for (const [key, context] of Object.entries(subjectContextDatabase)) {
      if (subjectKey.includes(key) || key.includes(subjectKey)) {
        return context;
      }
    }

    return null;
  }

  private static assessDifficultyLevel(subject: string, level: string, semester: number): number {
    const subjectKey = subject.toLowerCase();
    const contextData = this.findBestMatch(subjectKey);
    
    if (contextData?.difficultyLevel) {
      return contextData.difficultyLevel;
    }

    // Base difficulty on level and semester
    let difficulty = 5; // Base difficulty
    
    if (level === 'postgraduate') {
      difficulty += 2;
    }
    
    if (semester > 4) {
      difficulty += 1;
    }
    
    // Adjust for subject complexity
    const complexSubjects = ['machine learning', 'quantum', 'advanced', 'research'];
    if (complexSubjects.some(keyword => subjectKey.includes(keyword))) {
      difficulty += 1;
    }

    return Math.min(10, Math.max(1, difficulty));
  }
}
