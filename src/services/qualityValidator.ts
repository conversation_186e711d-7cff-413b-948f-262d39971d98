import { Course, CourseUnit, CourseOutcome } from '../types/course';

export interface ValidationResult {
  isValid: boolean;
  score: number; // 0-100
  issues: ValidationIssue[];
  suggestions: QualitySuggestion[];
  strengths: string[];
}

export interface ValidationIssue {
  type: 'error' | 'warning' | 'info';
  category: 'structure' | 'content' | 'compliance' | 'pedagogy';
  message: string;
  field?: string;
  severity: 'high' | 'medium' | 'low';
}

export interface QualitySuggestion {
  category: 'improvement' | 'enhancement' | 'best-practice';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
}

export class QualityValidator {
  static validateCourse(course: Course): ValidationResult {
    const issues: ValidationIssue[] = [];
    const suggestions: QualitySuggestion[] = [];
    const strengths: string[] = [];

    // Structural validation
    this.validateStructure(course, issues, strengths);
    
    // Content quality validation
    this.validateContent(course, issues, suggestions, strengths);
    
    // AICTE compliance validation
    this.validateCompliance(course, issues, suggestions, strengths);
    
    // Pedagogical validation
    this.validatePedagogy(course, issues, suggestions, strengths);

    // Calculate overall score
    const score = this.calculateQualityScore(course, issues);

    return {
      isValid: issues.filter(i => i.type === 'error').length === 0,
      score,
      issues,
      suggestions,
      strengths
    };
  }

  private static validateStructure(course: Course, issues: ValidationIssue[], strengths: string[]): void {
    // Check required fields
    if (!course.title || course.title.trim().length < 5) {
      issues.push({
        type: 'error',
        category: 'structure',
        message: 'Course title is too short or missing',
        field: 'title',
        severity: 'high'
      });
    } else {
      strengths.push('Well-defined course title');
    }

    if (!course.code || !/^[A-Z]{2,4}\d{3,4}$/.test(course.code)) {
      issues.push({
        type: 'warning',
        category: 'structure',
        message: 'Course code should follow standard format (e.g., CS301, ME205)',
        field: 'code',
        severity: 'medium'
      });
    }

    // Check units structure
    if (course.units.length < 4 || course.units.length > 6) {
      issues.push({
        type: 'warning',
        category: 'structure',
        message: 'Course should typically have 4-6 units for optimal learning',
        field: 'units',
        severity: 'medium'
      });
    } else {
      strengths.push('Appropriate number of course units');
    }

    // Check contact hours distribution
    const totalHours = course.contactHours.lecture + course.contactHours.tutorial + course.contactHours.practical;
    if (totalHours < 3 || totalHours > 8) {
      issues.push({
        type: 'warning',
        category: 'structure',
        message: 'Total contact hours should typically be between 3-8 per week',
        field: 'contactHours',
        severity: 'medium'
      });
    }
  }

  private static validateContent(course: Course, issues: ValidationIssue[], suggestions: QualitySuggestion[], strengths: string[]): void {
    // Check learning outcomes quality
    if (course.outcomes.length < 3 || course.outcomes.length > 8) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'Course should have 3-8 well-defined learning outcomes',
        field: 'outcomes',
        severity: 'medium'
      });
    }

    // Check Bloom's taxonomy distribution
    const bloomsLevels = course.outcomes.map(o => o.bloomsLevel.toLowerCase());
    const uniqueLevels = new Set(bloomsLevels);
    
    if (uniqueLevels.size < 3) {
      suggestions.push({
        category: 'improvement',
        title: 'Diversify Bloom\'s Taxonomy Levels',
        description: 'Include outcomes across different cognitive levels (Remember, Understand, Apply, Analyze, Evaluate, Create)',
        impact: 'high',
        effort: 'medium'
      });
    } else {
      strengths.push('Good distribution of Bloom\'s taxonomy levels');
    }

    // Check unit content depth
    course.units.forEach((unit, index) => {
      if (unit.topics.length < 4 || unit.topics.length > 10) {
        issues.push({
          type: 'info',
          category: 'content',
          message: `Unit ${index + 1} should have 4-10 topics for balanced coverage`,
          field: `units[${index}].topics`,
          severity: 'low'
        });
      }

      if (unit.contactHours < 6 || unit.contactHours > 15) {
        issues.push({
          type: 'warning',
          category: 'content',
          message: `Unit ${index + 1} contact hours should be 6-15 for effective learning`,
          field: `units[${index}].contactHours`,
          severity: 'medium'
        });
      }
    });

    // Check resource adequacy
    if (course.textBooks.length < 2) {
      suggestions.push({
        category: 'enhancement',
        title: 'Add More Textbook References',
        description: 'Include 2-3 primary textbooks for comprehensive coverage',
        impact: 'medium',
        effort: 'low'
      });
    }

    if (course.onlineResources.length < 3) {
      suggestions.push({
        category: 'enhancement',
        title: 'Include More Online Resources',
        description: 'Add credible online resources, MOOCs, and documentation links',
        impact: 'medium',
        effort: 'low'
      });
    }
  }

  private static validateCompliance(course: Course, issues: ValidationIssue[], suggestions: QualitySuggestion[], strengths: string[]): void {
    // Check AICTE credit requirements
    if (course.credits < 2 || course.credits > 6) {
      issues.push({
        type: 'warning',
        category: 'compliance',
        message: 'Course credits should be between 2-6 as per AICTE guidelines',
        field: 'credits',
        severity: 'medium'
      });
    }

    // Check assessment pattern
    const { internalAssessment, endSemExam, practical } = course.assessmentPattern;
    const total = internalAssessment + endSemExam + (practical || 0);
    
    if (Math.abs(total - 100) > 5) {
      issues.push({
        type: 'error',
        category: 'compliance',
        message: 'Assessment pattern should total 100%',
        field: 'assessmentPattern',
        severity: 'high'
      });
    }

    if (internalAssessment < 30 || internalAssessment > 50) {
      issues.push({
        type: 'warning',
        category: 'compliance',
        message: 'Internal assessment should typically be 30-50% as per AICTE guidelines',
        field: 'assessmentPattern.internalAssessment',
        severity: 'medium'
      });
    } else {
      strengths.push('AICTE-compliant assessment pattern');
    }

    // Check prerequisites appropriateness
    if (course.prerequisites.length === 0) {
      suggestions.push({
        category: 'best-practice',
        title: 'Define Course Prerequisites',
        description: 'Specify prerequisite courses or knowledge areas for better student preparation',
        impact: 'medium',
        effort: 'low'
      });
    }
  }

  private static validatePedagogy(course: Course, issues: ValidationIssue[], suggestions: QualitySuggestion[], strengths: string[]): void {
    // Check learning outcome measurability
    const vagueWords = ['understand', 'know', 'learn', 'appreciate', 'be familiar with'];
    let measurableOutcomes = 0;

    course.outcomes.forEach((outcome, index) => {
      const hasVagueWords = vagueWords.some(word => 
        outcome.description.toLowerCase().includes(word)
      );
      
      if (hasVagueWords) {
        suggestions.push({
          category: 'improvement',
          title: `Improve Outcome ${outcome.id} Measurability`,
          description: 'Use action verbs like analyze, design, implement, evaluate instead of vague terms',
          impact: 'high',
          effort: 'low'
        });
      } else {
        measurableOutcomes++;
      }
    });

    if (measurableOutcomes / course.outcomes.length > 0.7) {
      strengths.push('Well-defined measurable learning outcomes');
    }

    // Check progressive difficulty
    const unitHours = course.units.map(u => u.contactHours);
    const isProgressive = this.checkProgressiveDifficulty(unitHours);
    
    if (!isProgressive) {
      suggestions.push({
        category: 'improvement',
        title: 'Optimize Unit Progression',
        description: 'Consider arranging units in progressive difficulty order',
        impact: 'medium',
        effort: 'medium'
      });
    }

    // Check practical component
    if (course.contactHours.practical === 0 && course.courseType !== 'theory') {
      suggestions.push({
        category: 'enhancement',
        title: 'Add Practical Component',
        description: 'Include hands-on exercises, labs, or projects for better learning',
        impact: 'high',
        effort: 'high'
      });
    }
  }

  private static checkProgressiveDifficulty(unitHours: number[]): boolean {
    // Simple heuristic: later units shouldn't be significantly shorter
    if (unitHours.length < 3) return true;
    
    const firstHalf = unitHours.slice(0, Math.floor(unitHours.length / 2));
    const secondHalf = unitHours.slice(Math.floor(unitHours.length / 2));
    
    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
    
    return secondAvg >= firstAvg * 0.8; // Allow some variation
  }

  private static calculateQualityScore(course: Course, issues: ValidationIssue[]): number {
    let score = 100;
    
    // Deduct points for issues
    issues.forEach(issue => {
      switch (issue.severity) {
        case 'high':
          score -= issue.type === 'error' ? 15 : 10;
          break;
        case 'medium':
          score -= issue.type === 'error' ? 10 : 5;
          break;
        case 'low':
          score -= issue.type === 'error' ? 5 : 2;
          break;
      }
    });

    // Bonus points for good practices
    if (course.industryRelevance) score += 5;
    if (course.prerequisites.length > 0) score += 3;
    if (course.units.length === 5) score += 2;
    if (course.outcomes.length >= 4 && course.outcomes.length <= 6) score += 3;

    return Math.max(0, Math.min(100, score));
  }

  static generateImprovementReport(course: Course): string {
    const validation = this.validateCourse(course);
    
    let report = `# Course Quality Assessment Report\n\n`;
    report += `**Course:** ${course.title} (${course.code})\n`;
    report += `**Overall Score:** ${validation.score}/100\n\n`;

    if (validation.strengths.length > 0) {
      report += `## ✅ Strengths\n`;
      validation.strengths.forEach(strength => {
        report += `- ${strength}\n`;
      });
      report += `\n`;
    }

    if (validation.issues.length > 0) {
      report += `## ⚠️ Issues to Address\n`;
      validation.issues.forEach(issue => {
        const icon = issue.type === 'error' ? '🔴' : issue.type === 'warning' ? '🟡' : '🔵';
        report += `${icon} **${issue.category.toUpperCase()}**: ${issue.message}\n`;
      });
      report += `\n`;
    }

    if (validation.suggestions.length > 0) {
      report += `## 💡 Improvement Suggestions\n`;
      validation.suggestions.forEach(suggestion => {
        const impact = suggestion.impact === 'high' ? '🔥' : suggestion.impact === 'medium' ? '⚡' : '💫';
        report += `${impact} **${suggestion.title}**\n`;
        report += `   ${suggestion.description}\n`;
        report += `   *Impact: ${suggestion.impact} | Effort: ${suggestion.effort}*\n\n`;
      });
    }

    return report;
  }
}
