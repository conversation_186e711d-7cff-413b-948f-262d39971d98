import { Course, CourseGenerationRequest } from '../types/course';
import { getTemplateForSubject, enhancePromptWithTemplate } from './courseTemplates';
import { CourseContextService } from './courseContext';

// API URLs for different providers
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';
const ANTHROPIC_API_URL = 'https://api.anthropic.com/v1/messages';
const GOOGLE_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

export type LLMProvider = 'openai' | 'anthropic' | 'google';

export interface LLMConfig {
  provider: LLMProvider;
  apiKey: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export class LLMService {
  private config: LLMConfig;

  constructor(config: LLMConfig) {
    this.config = {
      temperature: 0.7,
      maxTokens: 4000,
      model: this.getDefaultModel(config.provider),
      ...config
    };
  }

  private getDefaultModel(provider: LLMProvider): string {
    switch (provider) {
      case 'openai':
        return 'gpt-4-turbo-preview';
      case 'anthropic':
        return 'claude-3-sonnet-20240229';
      case 'google':
        return 'gemini-pro';
      default:
        return 'gpt-4-turbo-preview';
    }
  }

  async generateCourse(request: CourseGenerationRequest): Promise<Course> {
    const prompt = this.buildEnhancedCoursePrompt(request);
    const systemPrompt = this.getSystemPrompt();

    try {
      const response = await this.callLLMAPI(systemPrompt, prompt);
      return this.parseCourseResponse(response, request);
    } catch (error) {
      console.error('Error generating course:', error);
      throw new Error('Failed to generate course. Please check your API key and try again.');
    }
  }

  // Public method for chatbot service
  async callLLMAPI(systemPrompt: string, userPrompt: string): Promise<string> {
    return this.callLLMAPIInternal(systemPrompt, userPrompt);
  }

  private async callLLMAPIInternal(systemPrompt: string, userPrompt: string): Promise<string> {
    switch (this.config.provider) {
      case 'openai':
        return this.callOpenAI(systemPrompt, userPrompt);
      case 'anthropic':
        return this.callAnthropic(systemPrompt, userPrompt);
      case 'google':
        return this.callGoogle(systemPrompt, userPrompt);
      default:
        throw new Error(`Unsupported provider: ${this.config.provider}`);
    }
  }

  private async callOpenAI(systemPrompt: string, userPrompt: string): Promise<string> {
    const response = await fetch(OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API request failed: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  private async callAnthropic(systemPrompt: string, userPrompt: string): Promise<string> {
    const response = await fetch(ANTHROPIC_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.config.apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model: this.config.model,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        system: systemPrompt,
        messages: [
          { role: 'user', content: userPrompt }
        ],
      }),
    });

    if (!response.ok) {
      throw new Error(`Anthropic API request failed: ${response.statusText}`);
    }

    const data = await response.json();
    return data.content[0].text;
  }

  private async callGoogle(systemPrompt: string, userPrompt: string): Promise<string> {
    const response = await fetch(`${GOOGLE_API_URL}?key=${this.config.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `${systemPrompt}\n\n${userPrompt}`
          }]
        }],
        generationConfig: {
          temperature: this.config.temperature,
          maxOutputTokens: this.config.maxTokens,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Google API request failed: ${response.statusText}`);
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  }

  private getSystemPrompt(): string {
    return `You are an expert curriculum designer and academic consultant specializing in Indian higher education with the following expertise:

1. Deep knowledge of AICTE (All India Council for Technical Education) guidelines and regulations
2. Understanding of UGC (University Grants Commission) standards and frameworks
3. Expertise in Bloom's Taxonomy and learning outcome design
4. Knowledge of industry requirements and current technological trends
5. Experience with Indian university assessment patterns and credit systems
6. Understanding of NEP 2020 (National Education Policy) implementation

Your role is to design comprehensive, industry-relevant, and academically rigorous course syllabi that:
- Meet AICTE/UGC compliance requirements
- Align with current industry needs and emerging technologies
- Follow pedagogical best practices
- Include measurable learning outcomes
- Provide practical application opportunities
- Support student career development

Always ensure your responses are professional, detailed, and suitable for official university documentation.`;
  }

  private buildEnhancedCoursePrompt(request: CourseGenerationRequest): string {
    const subjectContext = this.getSubjectContext(request.subject, request.department);
    const levelContext = this.getLevelContext(request.level, request.semester);
    const template = getTemplateForSubject(request.subject, request.department);
    const courseContext = CourseContextService.generateCourseContext(request);

    const basePrompt = `
Design a comprehensive, university-grade course syllabus for "${request.subject}" with the following specifications:

## Course Context
- **Academic Level**: ${request.level} (${levelContext})
- **Semester**: ${request.semester}
- **Duration**: ${request.duration} weeks
- **Course Type**: ${request.courseType}
- **Department**: ${request.department}
${request.specialization ? `- **Specialization**: ${request.specialization}` : ''}

## Subject Context
${subjectContext}

## Course Context Analysis
**Prerequisites Required:**
${courseContext.prerequisites.map(p => `- ${p.subject} (${p.level}): ${p.description}`).join('\n')}

**Industry Alignment:**
- **Key Skills:** ${courseContext.industryAlignment.skills.join(', ')}
- **Essential Tools:** ${courseContext.industryAlignment.tools.join(', ')}
- **Relevant Certifications:** ${courseContext.industryAlignment.certifications.join(', ')}
- **Career Paths:** ${courseContext.industryAlignment.careerPaths.join(', ')}
- **Industry Trends:** ${courseContext.industryAlignment.industryTrends.join(', ')}

**Course Progression Path:**
${courseContext.progressionPath.map((step, index) => `${index + 1}. ${step}`).join('\n')}

**Market Context:**
- **Difficulty Level:** ${courseContext.difficultyLevel}/10
- **Market Demand:** ${courseContext.marketDemand.toUpperCase()}
- **Emerging Trends:** ${courseContext.emergingTrends.join(', ')}

## Requirements for Syllabus Design

### 1. Academic Standards Compliance
- Follow AICTE guidelines for Indian technical education
- Align with UGC standards for higher education
- Implement NEP 2020 recommendations where applicable
- Ensure credit hour calculations match Indian university norms

### 2. Learning Design Principles
- Map all learning outcomes to Bloom's Taxonomy levels
- Ensure progressive difficulty across units
- Include both theoretical understanding and practical application
- Design for diverse learning styles and backgrounds

### 3. Industry Relevance
- Include current industry practices and technologies
- Reference latest tools, frameworks, and methodologies
- Align with job market requirements and career paths
- Include case studies from Indian and global contexts

### 4. Assessment Strategy
- Design comprehensive assessment patterns
- Include formative and summative evaluation methods
- Ensure assessments measure stated learning outcomes
- Follow Indian university grading and evaluation norms

Please provide your response in the following JSON format:
{
  "title": "Complete course title with proper academic naming",
  "code": "Course code following department standards (e.g., CS301, ME205)",
  "credits": 3,
  "contactHours": {
    "lecture": 3,
    "tutorial": 1,
    "practical": 2
  },
  "prerequisites": ["List of prerequisite courses with codes"],
  "corequisites": ["Courses to be taken simultaneously"],
  "industryRelevance": {
    "skills": ["Industry skill 1", "Industry skill 2"],
    "tools": ["Tool 1", "Tool 2"],
    "certifications": ["Certification 1", "Certification 2"],
    "careerOpportunities": ["Career path 1", "Career path 2"]
  },
  "marketDemand": "high",
  "difficultyLevel": 7,
  "objectives": [
    {"id": 1, "description": "Comprehensive course objective aligned with program outcomes"},
    {"id": 2, "description": "Industry-relevant objective with practical focus"}
  ],
  "outcomes": [
    {"id": "CO1", "description": "Detailed, measurable course outcome", "bloomsLevel": "Apply", "poMapping": ["PO1", "PO3"]},
    {"id": "CO2", "description": "Progressive learning outcome", "bloomsLevel": "Analyze", "poMapping": ["PO2", "PO4"]}
  ],
  "units": [
    {
      "unitNumber": 1,
      "title": "Comprehensive Unit Title",
      "topics": ["Detailed Topic 1", "Advanced Topic 2", "Practical Topic 3", "Industry Application 4", "Case Study 5", "Current Trends 6"],
      "learningOutcomes": ["Specific skill/knowledge 1", "Practical application 2", "Problem-solving ability 3"],
      "contactHours": 8,
      "practicalHours": 4,
      "assignments": ["Assignment description 1", "Project work 2"]
    }
  ],
  "textBooks": [
    {
      "title": "Recent, authoritative textbook title",
      "authors": ["Author 1", "Author 2"],
      "publisher": "Reputed publisher",
      "year": 2023,
      "isbn": "ISBN number",
      "edition": "Latest edition"
    }
  ],
  "referenceBooks": [
    {
      "title": "Additional reference book",
      "authors": ["Author"],
      "publisher": "Publisher",
      "year": 2022,
      "isbn": "ISBN",
      "edition": "Edition"
    }
  ],
  "onlineResources": [
    "https://credible-educational-resource-1.com",
    "https://industry-standard-documentation.org",
    "https://academic-journal-or-conference.edu"
  ],
  "assessmentPattern": {
    "internalAssessment": 40,
    "endSemExam": 60,
    "practical": 0,
    "assignments": 20,
    "quizzes": 10,
    "midterm": 10,
    "project": 0
  },
  "gradingScheme": "Standard university grading (A+, A, B+, B, C+, C, D, F)",
  "courseDelivery": {
    "lectureMethod": "Interactive lectures with multimedia",
    "practicalMethod": "Hands-on lab sessions",
    "assessmentMethod": "Continuous evaluation with feedback"
  }
}

## Detailed Requirements:

### Content Structure
1. **5 Comprehensive Units**: Each unit should build upon previous knowledge
2. **6-8 Detailed Topics per Unit**: Cover theoretical foundations, practical applications, and current trends
3. **Progressive Complexity**: Start with fundamentals, advance to complex applications
4. **Industry Integration**: Include real-world case studies and current industry practices

### Learning Outcomes Design
1. **Bloom's Taxonomy Alignment**: Distribute outcomes across Remember, Understand, Apply, Analyze, Evaluate, Create
2. **Measurable Outcomes**: Use action verbs and specific, quantifiable criteria
3. **Program Outcome Mapping**: Link to relevant Program Outcomes (PO1-PO12)
4. **Skill Development**: Include both technical and soft skills

### Resource Requirements
1. **Recent Textbooks**: Published within last 5 years from reputed publishers
2. **Diverse References**: Include books, journals, conference papers, and industry reports
3. **Online Resources**: Credible educational platforms, documentation, and professional resources
4. **Indian Context**: Include resources relevant to Indian industry and academic standards

### Assessment Design
1. **Continuous Evaluation**: Distribute marks across multiple assessment methods
2. **Practical Assessment**: Include hands-on evaluation for practical components
3. **Industry Relevance**: Design assessments that mirror real-world challenges
4. **Fair Distribution**: Balance theoretical and practical assessment components

### Quality Standards
1. **AICTE Compliance**: Follow all technical education guidelines
2. **University Standards**: Align with Indian university credit and grading systems
3. **Industry Readiness**: Prepare students for professional careers
4. **Academic Rigor**: Maintain high academic standards while being practical

Focus on creating a curriculum that produces industry-ready graduates while maintaining academic excellence, suitable for ${request.level} students in Indian universities.

IMPORTANT: Respond ONLY with the JSON object. Do not include any additional text, explanations, or formatting outside the JSON structure.
`;

    // Enhance the prompt with domain-specific template
    return enhancePromptWithTemplate(basePrompt, template, request.subject);
  }

  private getSubjectContext(subject: string, department: string): string {
    const subjectLower = subject.toLowerCase();

    // Engineering subjects
    if (subjectLower.includes('computer') || subjectLower.includes('software') || subjectLower.includes('programming')) {
      return `This is a Computer Science/IT course focusing on ${subject}. Emphasize programming skills, software development practices, current technologies, and industry-standard tools. Include practical coding exercises, project work, and exposure to latest frameworks and methodologies.`;
    }

    if (subjectLower.includes('data') || subjectLower.includes('machine learning') || subjectLower.includes('artificial intelligence')) {
      return `This is a Data Science/AI course covering ${subject}. Focus on mathematical foundations, statistical methods, programming implementation, and real-world applications. Include hands-on experience with datasets, tools like Python/R, and current industry practices.`;
    }

    if (subjectLower.includes('network') || subjectLower.includes('security') || subjectLower.includes('cyber')) {
      return `This is a Networking/Security course on ${subject}. Emphasize practical network configuration, security protocols, threat analysis, and hands-on lab work. Include current security challenges, industry standards, and compliance requirements.`;
    }

    if (subjectLower.includes('mechanical') || subjectLower.includes('thermal') || subjectLower.includes('manufacturing')) {
      return `This is a Mechanical Engineering course covering ${subject}. Focus on engineering principles, design methodologies, manufacturing processes, and practical applications. Include CAD/CAM tools, industry practices, and current technological developments.`;
    }

    if (subjectLower.includes('electrical') || subjectLower.includes('electronics') || subjectLower.includes('circuit')) {
      return `This is an Electrical/Electronics course on ${subject}. Emphasize circuit analysis, design principles, practical implementations, and modern applications. Include laboratory work, simulation tools, and current industry technologies.`;
    }

    // General academic subjects
    if (subjectLower.includes('mathematics') || subjectLower.includes('calculus') || subjectLower.includes('algebra')) {
      return `This is a Mathematics course covering ${subject}. Focus on theoretical foundations, problem-solving techniques, and practical applications. Include computational methods, real-world applications, and connections to other disciplines.`;
    }

    if (subjectLower.includes('physics') || subjectLower.includes('chemistry') || subjectLower.includes('biology')) {
      return `This is a Science course on ${subject}. Emphasize scientific principles, experimental methods, and practical applications. Include laboratory work, current research developments, and interdisciplinary connections.`;
    }

    // Business and management
    if (subjectLower.includes('management') || subjectLower.includes('business') || subjectLower.includes('marketing')) {
      return `This is a Management/Business course covering ${subject}. Focus on theoretical frameworks, case study analysis, and practical business applications. Include current industry trends, Indian business context, and global perspectives.`;
    }

    // Default context
    return `This course covers ${subject} in the context of ${department}. Focus on both theoretical understanding and practical application, ensuring students gain comprehensive knowledge and skills relevant to their field of study and future careers.`;
  }

  private getLevelContext(level: string, semester: number): string {
    if (level === 'undergraduate') {
      if (semester <= 2) {
        return 'Foundation level - Focus on basic concepts, fundamental principles, and building strong foundational knowledge';
      } else if (semester <= 4) {
        return 'Intermediate level - Build upon foundations with more complex concepts and practical applications';
      } else if (semester <= 6) {
        return 'Advanced level - Specialized knowledge with industry applications and project work';
      } else {
        return 'Final year level - Advanced specialization, capstone projects, and industry readiness';
      }
    } else {
      if (semester <= 2) {
        return 'Postgraduate foundation - Advanced concepts building on undergraduate knowledge with research orientation';
      } else {
        return 'Postgraduate advanced - Specialized research-oriented content with thesis/project components';
      }
    }
  }

  private parseCourseResponse(response: string, request: CourseGenerationRequest): Course {
    try {
      // Clean and extract JSON from the response
      let jsonString = response.trim();

      // Remove any markdown code blocks
      jsonString = jsonString.replace(/```json\s*/, '').replace(/```\s*$/, '');

      // Extract JSON object if there's additional text
      const jsonMatch = jsonString.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonString = jsonMatch[0];
      }

      const parsedData = JSON.parse(jsonString);

      // Validate required fields
      this.validateCourseData(parsedData);

      // Enhance the parsed data with additional metadata
      const enhancedCourse: Course = {
        id: `course_${Date.now()}`,
        ...parsedData,
        createdAt: new Date(),
        generatedBy: `AI Course Designer (${this.config.provider.toUpperCase()})`,
        // Ensure backward compatibility with existing structure
        assessmentPattern: {
          internalAssessment: parsedData.assessmentPattern?.internalAssessment || 40,
          endSemExam: parsedData.assessmentPattern?.endSemExam || 60,
          practical: parsedData.assessmentPattern?.practical || 0
        }
      };

      return enhancedCourse;
    } catch (error) {
      console.error('Error parsing course response:', error);
      console.error('Response content:', response);
      throw new Error('Failed to parse course data. The AI response may be malformed. Please try again.');
    }
  }

  private validateCourseData(data: any): void {
    const requiredFields = ['title', 'code', 'credits', 'objectives', 'outcomes', 'units'];

    for (const field of requiredFields) {
      if (!data[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate units structure
    if (!Array.isArray(data.units) || data.units.length === 0) {
      throw new Error('Course must have at least one unit');
    }

    // Validate outcomes structure
    if (!Array.isArray(data.outcomes) || data.outcomes.length === 0) {
      throw new Error('Course must have at least one learning outcome');
    }

    // Validate each unit has required fields
    data.units.forEach((unit: any, index: number) => {
      if (!unit.title || !unit.topics || !Array.isArray(unit.topics)) {
        throw new Error(`Unit ${index + 1} is missing required fields (title, topics)`);
      }
    });
  }
}

// Mock service for demonstration when API key is not available
export class MockLLMService {
  private config: LLMConfig;

  constructor() {
    this.config = {
      provider: 'openai',
      apiKey: 'demo',
      model: 'gpt-4-demo',
      temperature: 0.7,
      maxTokens: 4000
    };
  }

  async generateCourse(request: CourseGenerationRequest): Promise<Course> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 3000));

    const mockCourse = this.generateMockCourse(request);
    return mockCourse;
  }

  private generateMockCourse(request: CourseGenerationRequest): Course {
    const subjectTitle = request.subject;
    
    return {
      id: `course_${Date.now()}`,
      title: `${subjectTitle}`,
      code: `CS${300 + request.semester}`,
      credits: request.courseType === 'practical' ? 2 : 4,
      contactHours: {
        lecture: request.courseType === 'practical' ? 1 : 3,
        tutorial: 1,
        practical: request.courseType === 'practical' ? 4 : 2
      },
      prerequisites: ['Data Structures and Algorithms', 'Computer Programming', 'Mathematics for Computer Science'],
      objectives: [
        { id: 1, description: `Understand the fundamental concepts, principles, and theoretical foundations of ${subjectTitle}` },
        { id: 2, description: `Apply theoretical knowledge and methodologies to solve complex practical problems in ${subjectTitle}` },
        { id: 3, description: `Analyze and evaluate different approaches, algorithms, and methodologies used in ${subjectTitle}` },
        { id: 4, description: `Design and implement efficient solutions using advanced ${subjectTitle} concepts and techniques` },
        { id: 5, description: `Critically assess the performance, scalability, and optimization aspects of ${subjectTitle} solutions` }
      ],
      outcomes: [
        { id: 'CO1', description: `Explain core concepts, terminology, and fundamental principles of ${subjectTitle}`, bloomsLevel: 'Understand' },
        { id: 'CO2', description: `Apply ${subjectTitle} principles and algorithms to solve real-world computational problems`, bloomsLevel: 'Apply' },
        { id: 'CO3', description: `Analyze the complexity, performance, and efficiency of various ${subjectTitle} solutions and approaches`, bloomsLevel: 'Analyze' },
        { id: 'CO4', description: `Evaluate different methodologies and select optimal solutions for specific ${subjectTitle} applications`, bloomsLevel: 'Evaluate' },
        { id: 'CO5', description: `Create innovative and efficient solutions using advanced ${subjectTitle} concepts and emerging technologies`, bloomsLevel: 'Create' }
      ],
      units: [
        {
          unitNumber: 1,
          title: `Fundamentals and Introduction to ${subjectTitle}`,
          topics: [
            'Historical evolution and development of the field',
            'Basic concepts, definitions, and terminology',
            'Mathematical foundations and theoretical background',
            'Key principles and fundamental algorithms',
            'Classification and categorization of approaches',
            'Applications and real-world use cases',
            'Current trends and emerging technologies',
            'Industry standards and best practices'
          ],
          learningOutcomes: [
            'Understand the historical context and evolution of the field',
            'Recognize and explain fundamental concepts and terminology',
            'Apply basic principles to simple problem scenarios'
          ],
          contactHours: 10
        },
        {
          unitNumber: 2,
          title: `Core Algorithms and Data Structures in ${subjectTitle}`,
          topics: [
            'Essential data structures and their implementations',
            'Fundamental algorithms and their complexity analysis',
            'Optimization techniques and performance considerations',
            'Memory management and resource allocation',
            'Parallel and concurrent processing approaches',
            'Error handling and exception management',
            'Testing and validation methodologies',
            'Debugging and troubleshooting techniques'
          ],
          learningOutcomes: [
            'Implement and analyze core algorithms and data structures',
            'Evaluate algorithmic complexity and performance metrics',
            'Design efficient solutions for computational problems'
          ],
          contactHours: 12
        },
        {
          unitNumber: 3,
          title: `Advanced Concepts and Methodologies in ${subjectTitle}`,
          topics: [
            'Advanced algorithmic techniques and strategies',
            'Complex data structures and their applications',
            'Machine learning and artificial intelligence integration',
            'Distributed systems and scalability considerations',
            'Security aspects and vulnerability management',
            'Performance optimization and tuning strategies',
            'Advanced mathematical models and computational methods',
            'Research methodologies and experimental design'
          ],
          learningOutcomes: [
            'Apply advanced techniques to solve complex problems',
            'Analyze and optimize system performance and scalability',
            'Integrate modern technologies and methodologies'
          ],
          contactHours: 14
        },
        {
          unitNumber: 4,
          title: `Practical Applications and Case Studies in ${subjectTitle}`,
          topics: [
            'Industry case studies and real-world implementations',
            'Project development lifecycle and management',
            'Software engineering principles and practices',
            'Quality assurance and testing frameworks',
            'Documentation and maintenance strategies',
            'User interface design and user experience considerations',
            'Integration with existing systems and platforms',
            'Deployment and production environment considerations'
          ],
          learningOutcomes: [
            'Apply knowledge to develop complete practical solutions',
            'Evaluate real-world implementations and case studies',
            'Design and manage complex software projects'
          ],
          contactHours: 12
        },
        {
          unitNumber: 5,
          title: `Emerging Trends and Future Directions in ${subjectTitle}`,
          topics: [
            'Current research trends and breakthrough technologies',
            'Future applications and potential developments',
            'Interdisciplinary approaches and cross-domain integration',
            'Ethical considerations and social implications',
            'Sustainability and environmental impact assessment',
            'Innovation opportunities and entrepreneurial aspects',
            'Global perspectives and international standards',
            'Career opportunities and professional development'
          ],
          learningOutcomes: [
            'Evaluate emerging trends and future technological directions',
            'Create innovative solutions incorporating cutting-edge technologies',
            'Assess ethical and social implications of technological advancement'
          ],
          contactHours: 10
        }
      ],
      textBooks: [
        {
          title: `Comprehensive Guide to ${subjectTitle}: Theory and Practice`,
          authors: ['Dr. Academic Expert', 'Prof. Industry Specialist'],
          publisher: 'McGraw-Hill Education India',
          year: 2023,
          isbn: '978-0123456789'
        },
        {
          title: `${subjectTitle}: Algorithms, Applications, and Advanced Techniques`,
          authors: ['Research Scholar', 'Technical Author'],
          publisher: 'Pearson Education India',
          year: 2022,
          isbn: '978-0987654321'
        },
        {
          title: `Modern Approaches to ${subjectTitle}: A Comprehensive Textbook`,
          authors: ['Dr. University Professor'],
          publisher: 'Cengage Learning India',
          year: 2023,
          isbn: '978-0456789123'
        }
      ],
      referenceBooks: [
        {
          title: `Advanced ${subjectTitle}: Research Perspectives and Innovations`,
          authors: ['International Expert'],
          publisher: 'Wiley India Private Limited',
          year: 2023
        },
        {
          title: `${subjectTitle} Handbook: Complete Reference Guide`,
          authors: ['Technical Committee'],
          publisher: 'PHI Learning Private Limited',
          year: 2022
        },
        {
          title: `Practical ${subjectTitle}: Implementation and Case Studies`,
          authors: ['Industry Practitioner'],
          publisher: 'Oxford University Press India',
          year: 2023
        },
        {
          title: `${subjectTitle} for Engineers: A Comprehensive Approach`,
          authors: ['Engineering Faculty'],
          publisher: 'Tata McGraw-Hill Education',
          year: 2022
        }
      ],
      onlineResources: [
        'https://www.coursera.org/specializations/computer-science',
        'https://www.edx.org/course/introduction-computer-science',
        'https://www.khanacademy.org/computing',
        'https://ocw.mit.edu/courses/electrical-engineering-and-computer-science/',
        'https://www.nptel.ac.in/courses/computer-science-and-engineering',
        'https://www.geeksforgeeks.org/',
        'https://leetcode.com/',
        'https://www.hackerrank.com/'
      ],
      assessmentPattern: {
        internalAssessment: 40,
        endSemExam: 60,
        practical: request.courseType === 'practical' ? 50 : 0
      },
      createdAt: new Date(),
      generatedBy: 'AI Course Designer (Demo Mode)'
    };
  }
}