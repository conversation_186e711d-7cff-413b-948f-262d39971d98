import { Course, CourseGenerationRequest } from '../types/course';

const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

export class LLMService {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateCourse(request: CourseGenerationRequest): Promise<Course> {
    const prompt = this.buildCoursePrompt(request);

    try {
      const response = await fetch(OPENAI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: 'gpt-4-turbo-preview',
          messages: [
            {
              role: 'system',
              content: 'You are an expert curriculum designer specializing in Indian university education with deep knowledge of AICTE guidelines and academic standards.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 4000,
          temperature: 0.7,
        }),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      const data = await response.json();
      const courseContent = data.choices[0].message.content;
      
      return this.parseCourseResponse(courseContent, request);
    } catch (error) {
      console.error('Error generating course:', error);
      throw new Error('Failed to generate course. Please check your API key and try again.');
    }
  }

  private buildCoursePrompt(request: CourseGenerationRequest): string {
    return `
Design a comprehensive course syllabus for "${request.subject}" suitable for Indian universities with the following specifications:

Course Details:
- Level: ${request.level}
- Semester: ${request.semester}
- Duration: ${request.duration} weeks
- Type: ${request.courseType}
- Department: ${request.department}
${request.specialization ? `- Specialization: ${request.specialization}` : ''}

Please provide a detailed response in JSON format with the following structure:
{
  "title": "Complete course title",
  "code": "Course code (e.g., CS301)",
  "credits": 3,
  "contactHours": {
    "lecture": 3,
    "tutorial": 1,
    "practical": 2
  },
  "prerequisites": ["List of prerequisite courses"],
  "objectives": [
    {"id": 1, "description": "Course objective 1"},
    {"id": 2, "description": "Course objective 2"}
  ],
  "outcomes": [
    {"id": "CO1", "description": "Course outcome 1", "bloomsLevel": "Apply"},
    {"id": "CO2", "description": "Course outcome 2", "bloomsLevel": "Analyze"}
  ],
  "units": [
    {
      "unitNumber": 1,
      "title": "Unit 1 Title",
      "topics": ["Topic 1", "Topic 2", "Topic 3", "Topic 4", "Topic 5", "Topic 6"],
      "learningOutcomes": ["What students will learn 1", "What students will learn 2", "What students will learn 3"],
      "contactHours": 8
    }
  ],
  "textBooks": [
    {
      "title": "Book Title",
      "authors": ["Author 1", "Author 2"],
      "publisher": "Publisher Name",
      "year": 2023
    }
  ],
  "referenceBooks": [Similar structure to textBooks],
  "onlineResources": ["URL1", "URL2"],
  "assessmentPattern": {
    "internalAssessment": 40,
    "endSemExam": 60,
    "practical": 0
  }
}

Requirements:
1. Follow AICTE guidelines for Indian universities
2. Include 5 comprehensive units covering the subject fundamentals
3. Each unit should have 6-8 detailed topics covering all aspects
4. Ensure learning outcomes map to Bloom's taxonomy
5. Provide appropriate contact hours distribution
6. Include recent and relevant textbooks and references
7. Add credible online resources and websites
8. Design assessment pattern suitable for Indian academic system
9. Ensure course outcomes are measurable and achievable
10. Make topics very detailed and comprehensive

Focus on creating a curriculum that balances theoretical understanding with practical application, suitable for ${request.level} students in India.
`;
  }

  private parseCourseResponse(response: string, request: CourseGenerationRequest): Course {
    try {
      // Extract JSON from the response (in case there's additional text)
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : response;
      const parsedData = JSON.parse(jsonString);

      return {
        id: `course_${Date.now()}`,
        ...parsedData,
        createdAt: new Date(),
        generatedBy: 'AI Course Designer'
      };
    } catch (error) {
      console.error('Error parsing course response:', error);
      throw new Error('Failed to parse course data. Please try again.');
    }
  }
}

// Mock service for demonstration when API key is not available
export class MockLLMService {
  async generateCourse(request: CourseGenerationRequest): Promise<Course> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 3000));

    const mockCourse = this.generateMockCourse(request);
    return mockCourse;
  }

  private generateMockCourse(request: CourseGenerationRequest): Course {
    const subjectTitle = request.subject;
    
    return {
      id: `course_${Date.now()}`,
      title: `${subjectTitle}`,
      code: `CS${300 + request.semester}`,
      credits: request.courseType === 'practical' ? 2 : 4,
      contactHours: {
        lecture: request.courseType === 'practical' ? 1 : 3,
        tutorial: 1,
        practical: request.courseType === 'practical' ? 4 : 2
      },
      prerequisites: ['Data Structures and Algorithms', 'Computer Programming', 'Mathematics for Computer Science'],
      objectives: [
        { id: 1, description: `Understand the fundamental concepts, principles, and theoretical foundations of ${subjectTitle}` },
        { id: 2, description: `Apply theoretical knowledge and methodologies to solve complex practical problems in ${subjectTitle}` },
        { id: 3, description: `Analyze and evaluate different approaches, algorithms, and methodologies used in ${subjectTitle}` },
        { id: 4, description: `Design and implement efficient solutions using advanced ${subjectTitle} concepts and techniques` },
        { id: 5, description: `Critically assess the performance, scalability, and optimization aspects of ${subjectTitle} solutions` }
      ],
      outcomes: [
        { id: 'CO1', description: `Explain core concepts, terminology, and fundamental principles of ${subjectTitle}`, bloomsLevel: 'Understand' },
        { id: 'CO2', description: `Apply ${subjectTitle} principles and algorithms to solve real-world computational problems`, bloomsLevel: 'Apply' },
        { id: 'CO3', description: `Analyze the complexity, performance, and efficiency of various ${subjectTitle} solutions and approaches`, bloomsLevel: 'Analyze' },
        { id: 'CO4', description: `Evaluate different methodologies and select optimal solutions for specific ${subjectTitle} applications`, bloomsLevel: 'Evaluate' },
        { id: 'CO5', description: `Create innovative and efficient solutions using advanced ${subjectTitle} concepts and emerging technologies`, bloomsLevel: 'Create' }
      ],
      units: [
        {
          unitNumber: 1,
          title: `Fundamentals and Introduction to ${subjectTitle}`,
          topics: [
            'Historical evolution and development of the field',
            'Basic concepts, definitions, and terminology',
            'Mathematical foundations and theoretical background',
            'Key principles and fundamental algorithms',
            'Classification and categorization of approaches',
            'Applications and real-world use cases',
            'Current trends and emerging technologies',
            'Industry standards and best practices'
          ],
          learningOutcomes: [
            'Understand the historical context and evolution of the field',
            'Recognize and explain fundamental concepts and terminology',
            'Apply basic principles to simple problem scenarios'
          ],
          contactHours: 10
        },
        {
          unitNumber: 2,
          title: `Core Algorithms and Data Structures in ${subjectTitle}`,
          topics: [
            'Essential data structures and their implementations',
            'Fundamental algorithms and their complexity analysis',
            'Optimization techniques and performance considerations',
            'Memory management and resource allocation',
            'Parallel and concurrent processing approaches',
            'Error handling and exception management',
            'Testing and validation methodologies',
            'Debugging and troubleshooting techniques'
          ],
          learningOutcomes: [
            'Implement and analyze core algorithms and data structures',
            'Evaluate algorithmic complexity and performance metrics',
            'Design efficient solutions for computational problems'
          ],
          contactHours: 12
        },
        {
          unitNumber: 3,
          title: `Advanced Concepts and Methodologies in ${subjectTitle}`,
          topics: [
            'Advanced algorithmic techniques and strategies',
            'Complex data structures and their applications',
            'Machine learning and artificial intelligence integration',
            'Distributed systems and scalability considerations',
            'Security aspects and vulnerability management',
            'Performance optimization and tuning strategies',
            'Advanced mathematical models and computational methods',
            'Research methodologies and experimental design'
          ],
          learningOutcomes: [
            'Apply advanced techniques to solve complex problems',
            'Analyze and optimize system performance and scalability',
            'Integrate modern technologies and methodologies'
          ],
          contactHours: 14
        },
        {
          unitNumber: 4,
          title: `Practical Applications and Case Studies in ${subjectTitle}`,
          topics: [
            'Industry case studies and real-world implementations',
            'Project development lifecycle and management',
            'Software engineering principles and practices',
            'Quality assurance and testing frameworks',
            'Documentation and maintenance strategies',
            'User interface design and user experience considerations',
            'Integration with existing systems and platforms',
            'Deployment and production environment considerations'
          ],
          learningOutcomes: [
            'Apply knowledge to develop complete practical solutions',
            'Evaluate real-world implementations and case studies',
            'Design and manage complex software projects'
          ],
          contactHours: 12
        },
        {
          unitNumber: 5,
          title: `Emerging Trends and Future Directions in ${subjectTitle}`,
          topics: [
            'Current research trends and breakthrough technologies',
            'Future applications and potential developments',
            'Interdisciplinary approaches and cross-domain integration',
            'Ethical considerations and social implications',
            'Sustainability and environmental impact assessment',
            'Innovation opportunities and entrepreneurial aspects',
            'Global perspectives and international standards',
            'Career opportunities and professional development'
          ],
          learningOutcomes: [
            'Evaluate emerging trends and future technological directions',
            'Create innovative solutions incorporating cutting-edge technologies',
            'Assess ethical and social implications of technological advancement'
          ],
          contactHours: 10
        }
      ],
      textBooks: [
        {
          title: `Comprehensive Guide to ${subjectTitle}: Theory and Practice`,
          authors: ['Dr. Academic Expert', 'Prof. Industry Specialist'],
          publisher: 'McGraw-Hill Education India',
          year: 2023,
          isbn: '978-0123456789'
        },
        {
          title: `${subjectTitle}: Algorithms, Applications, and Advanced Techniques`,
          authors: ['Research Scholar', 'Technical Author'],
          publisher: 'Pearson Education India',
          year: 2022,
          isbn: '978-0987654321'
        },
        {
          title: `Modern Approaches to ${subjectTitle}: A Comprehensive Textbook`,
          authors: ['Dr. University Professor'],
          publisher: 'Cengage Learning India',
          year: 2023,
          isbn: '978-0456789123'
        }
      ],
      referenceBooks: [
        {
          title: `Advanced ${subjectTitle}: Research Perspectives and Innovations`,
          authors: ['International Expert'],
          publisher: 'Wiley India Private Limited',
          year: 2023
        },
        {
          title: `${subjectTitle} Handbook: Complete Reference Guide`,
          authors: ['Technical Committee'],
          publisher: 'PHI Learning Private Limited',
          year: 2022
        },
        {
          title: `Practical ${subjectTitle}: Implementation and Case Studies`,
          authors: ['Industry Practitioner'],
          publisher: 'Oxford University Press India',
          year: 2023
        },
        {
          title: `${subjectTitle} for Engineers: A Comprehensive Approach`,
          authors: ['Engineering Faculty'],
          publisher: 'Tata McGraw-Hill Education',
          year: 2022
        }
      ],
      onlineResources: [
        'https://www.coursera.org/specializations/computer-science',
        'https://www.edx.org/course/introduction-computer-science',
        'https://www.khanacademy.org/computing',
        'https://ocw.mit.edu/courses/electrical-engineering-and-computer-science/',
        'https://www.nptel.ac.in/courses/computer-science-and-engineering',
        'https://www.geeksforgeeks.org/',
        'https://leetcode.com/',
        'https://www.hackerrank.com/'
      ],
      assessmentPattern: {
        internalAssessment: 40,
        endSemExam: 60,
        practical: request.courseType === 'practical' ? 50 : 0
      },
      createdAt: new Date(),
      generatedBy: 'AI Course Designer (Demo Mode)'
    };
  }
}