import { <PERSON>MService, LLMConfig, LLMProvider } from './llmService';

export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  isTyping?: boolean;
}

export interface ChatbotConfig {
  llmConfig?: LLMConfig;
  useLLM: boolean;
}

export class ChatbotService {
  private llmService?: LLMService;
  private useLLM: boolean;
  private conversationHistory: ChatMessage[] = [];

  constructor(config: ChatbotConfig) {
    this.useLLM = config.useLLM;
    if (config.llmConfig && config.useLLM) {
      this.llmService = new LLMService(config.llmConfig);
    }
  }

  async generateResponse(userMessage: string): Promise<string> {
    // Add user message to conversation history
    this.conversationHistory.push({
      id: Date.now().toString(),
      text: userMessage,
      sender: 'user',
      timestamp: new Date()
    });

    if (this.useLLM && this.llmService) {
      return await this.generateLLMResponse(userMessage);
    } else {
      return this.generateRuleBasedResponse(userMessage);
    }
  }

  private async generateLLMResponse(userMessage: string): Promise<string> {
    try {
      const systemPrompt = this.getChatbotSystemPrompt();
      const contextualPrompt = this.buildContextualPrompt(userMessage);
      
      const response = await this.llmService!.callLLMAPI(systemPrompt, contextualPrompt);
      
      // Add bot response to conversation history
      this.conversationHistory.push({
        id: (Date.now() + 1).toString(),
        text: response,
        sender: 'bot',
        timestamp: new Date()
      });

      return response;
    } catch (error) {
      console.error('Error generating LLM response:', error);
      return this.generateRuleBasedResponse(userMessage);
    }
  }

  private getChatbotSystemPrompt(): string {
    return `You are a professional Course Design Assistant specializing in Indian higher education. Your expertise includes:

**Core Competencies:**
- AICTE (All India Council for Technical Education) guidelines and compliance
- UGC (University Grants Commission) standards and frameworks
- NEP 2020 (National Education Policy) implementation
- Bloom's Taxonomy and learning outcome design
- Indian university credit systems and assessment patterns
- Curriculum design best practices
- Industry-academia alignment
- Academic quality assurance

**Communication Style:**
- Professional and knowledgeable
- Provide specific, actionable advice
- Reference official guidelines when relevant
- Use clear, academic language
- Be helpful and supportive
- Acknowledge limitations when appropriate

**Key Areas of Assistance:**
1. Course structure and credit allocation
2. Learning objectives and outcomes design
3. Assessment pattern recommendations
4. AICTE compliance requirements
5. Unit planning and topic organization
6. Textbook and resource selection
7. Industry relevance and skill development
8. Academic calendar and scheduling

**Response Guidelines:**
- Keep responses concise but comprehensive
- Provide practical examples when helpful
- Reference specific AICTE/UGC guidelines when applicable
- Suggest next steps or follow-up actions
- Maintain professional academic tone
- If unsure about specific regulations, recommend consulting official sources

You are here to help educators create high-quality, compliant, and effective course curricula for Indian universities.`;
  }

  private buildContextualPrompt(userMessage: string): string {
    // Get recent conversation context (last 6 messages)
    const recentHistory = this.conversationHistory.slice(-6);
    let contextPrompt = '';

    if (recentHistory.length > 0) {
      contextPrompt = '\n**Conversation Context:**\n';
      recentHistory.forEach(msg => {
        contextPrompt += `${msg.sender === 'user' ? 'User' : 'Assistant'}: ${msg.text}\n`;
      });
      contextPrompt += '\n';
    }

    return `${contextPrompt}**Current Question:** ${userMessage}

Please provide a helpful, professional response based on your expertise in Indian higher education and course design. If the question is outside your domain, politely redirect to course design topics.`;
  }

  private generateRuleBasedResponse(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase();
    
    // Enhanced rule-based responses with more comprehensive coverage
    const responses = {
      // AICTE Guidelines
      'aicte': 'AICTE guidelines ensure quality technical education in India. Key requirements include: minimum 75% attendance, continuous assessment (40% internal + 60% external), qualified faculty with PhD/industry experience, adequate infrastructure, and industry-relevant curriculum. For specific compliance details, refer to the latest AICTE handbook.',
      
      'compliance': 'AICTE compliance involves: 1) Faculty qualifications (PhD/M.Tech with industry experience), 2) Infrastructure standards (labs, library, classrooms), 3) Curriculum approval, 4) Assessment patterns (40:60 internal:external), 5) Student-faculty ratio, 6) Industry partnerships. Regular audits ensure adherence.',
      
      // Course Structure
      'credits': 'Indian universities typically follow: Theory courses (3-4 credits), Practical/Lab courses (1-2 credits), Project work (2-6 credits). Total credits per semester: 20-24 for undergraduate, 16-20 for postgraduate. Contact hours: 1 credit = 15-16 contact hours per semester.',
      
      'contact hours': 'Contact hour distribution: Lecture (L), Tutorial (T), Practical (P) format. Example: L-T-P = 3-1-2 means 3 hours lecture, 1 hour tutorial, 2 hours practical per week. Total contact hours = (L+T+P) × weeks in semester.',
      
      // Learning Outcomes
      'learning outcomes': 'Learning outcomes should be SMART (Specific, Measurable, Achievable, Relevant, Time-bound) and mapped to Bloom\'s taxonomy levels: Remember, Understand, Apply, Analyze, Evaluate, Create. Each course should have 4-6 clear outcomes linked to program outcomes.',
      
      'bloom\'s taxonomy': 'Bloom\'s levels for course outcomes: 1) Remember (recall facts), 2) Understand (explain concepts), 3) Apply (use knowledge), 4) Analyze (break down problems), 5) Evaluate (make judgments), 6) Create (produce new work). Distribute outcomes across these levels.',
      
      // Assessment
      'assessment': 'Indian university assessment pattern: Internal Assessment (40%) includes assignments, quizzes, mid-term exams, attendance. End Semester Exam (60%) tests comprehensive understanding. Practical courses may have 50% internal, 50% external evaluation.',
      
      'evaluation': 'Evaluation methods: Formative (ongoing feedback) and Summative (final grades). Use diverse methods: written exams, practical tests, projects, presentations, assignments. Ensure alignment with learning outcomes and fair distribution of marks.',
      
      // Curriculum Design
      'curriculum': 'Curriculum design principles: 1) Align with program objectives, 2) Progressive difficulty, 3) Theory-practice balance, 4) Industry relevance, 5) Interdisciplinary connections, 6) Skill development focus, 7) Regular updates based on feedback.',
      
      'units': 'Course units should be: 1) Logically sequenced, 2) Balanced in content load, 3) 8-12 contact hours each, 4) Include 6-8 detailed topics, 5) Have specific learning outcomes, 6) Connect to overall course objectives.',
      
      // Resources
      'textbooks': 'Textbook selection criteria: 1) Recent publication (within 5 years), 2) Reputed authors and publishers, 3) Comprehensive coverage, 4) Suitable for Indian context, 5) Available in libraries, 6) Supplemented with reference books and online resources.',
      
      'resources': 'Course resources should include: Primary textbooks (2-3), Reference books (4-5), Online resources (MOOCs, documentation), Research papers, Industry reports, Case studies. Ensure accessibility and relevance to Indian students.',
      
      // NEP 2020
      'nep 2020': 'NEP 2020 emphasizes: Multidisciplinary education, Choice-based credit system, Multiple entry-exit points, Skill development, Research orientation, Technology integration, Continuous assessment, Holistic development. Implement gradually with institutional support.',
      
      // Industry Alignment
      'industry': 'Industry alignment strategies: 1) Industry expert guest lectures, 2) Live projects and internships, 3) Current technology integration, 4) Skill-based learning, 5) Industry mentorship, 6) Regular curriculum review with industry feedback.',
      
      // Quality Assurance
      'quality': 'Quality assurance involves: 1) Regular curriculum review, 2) Faculty development programs, 3) Student feedback analysis, 4) Industry input, 5) Outcome-based education, 6) Continuous improvement, 7) Accreditation compliance (NBA/NAAC).'
    };

    // Check for keyword matches
    for (const [keyword, response] of Object.entries(responses)) {
      if (lowerMessage.includes(keyword)) {
        return response;
      }
    }

    // Greeting responses
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
      return 'Hello! I\'m your Course Design Assistant. I can help you with AICTE guidelines, curriculum design, learning outcomes, assessment patterns, and other aspects of Indian higher education. What would you like to know?';
    }

    // Help responses
    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do')) {
      return `I can assist you with:

📚 **Course Design**: Structure, credits, contact hours, unit planning
🎯 **Learning Outcomes**: Bloom's taxonomy, measurable objectives
📊 **Assessment**: Internal/external patterns, evaluation methods
📋 **AICTE Compliance**: Guidelines, requirements, standards
📖 **Resources**: Textbook selection, reference materials
🏭 **Industry Alignment**: Current trends, skill development
🔍 **Quality Assurance**: Best practices, continuous improvement

What specific topic would you like to explore?`;
    }

    // Default response
    return `That's an interesting question about course design! While I have knowledge about curriculum development, AICTE guidelines, and Indian higher education standards, I'd recommend consulting the latest official guidelines for specific technical details. 

Is there anything specific about course structure, learning outcomes, assessment patterns, or AICTE compliance I can help you with?`;
  }

  clearHistory(): void {
    this.conversationHistory = [];
  }

  getConversationHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }
}
