import React, { useState } from 'react';
import { BookOpen, GraduationCap, Clock, Users, Sparkles } from 'lucide-react';
import { CourseGenerationRequest } from '../types/course';

interface CourseFormProps {
  onSubmit: (request: CourseGenerationRequest) => void;
  isLoading: boolean;
}

export const CourseForm: React.FC<CourseFormProps> = ({ onSubmit, isLoading }) => {
  const [formData, setFormData] = useState<CourseGenerationRequest>({
    subject: '',
    level: 'undergraduate',
    semester: 1,
    duration: 16,
    courseType: 'theory',
    department: '',
    specialization: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleInputChange = (field: keyof CourseGenerationRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 max-w-4xl mx-auto border border-gray-700/50">
      <div className="flex items-center gap-4 mb-8">
        <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-4 rounded-2xl shadow-lg">
          <BookOpen className="w-8 h-8 text-white" />
        </div>
        <div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
            Course Generation
          </h2>
          <p className="text-gray-400 text-lg">Create comprehensive course syllabi with AI assistance</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              Subject/Course Name *
            </label>
            <input
              type="text"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              placeholder="e.g., Operating Systems, Machine Learning"
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 placeholder-gray-500 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
              required
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              Department *
            </label>
            <input
              type="text"
              value={formData.department}
              onChange={(e) => handleInputChange('department', e.target.value)}
              placeholder="e.g., Computer Science and Engineering"
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 placeholder-gray-500 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
              required
            />
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              <GraduationCap className="w-4 h-4 inline mr-2" />
              Level
            </label>
            <select
              value={formData.level}
              onChange={(e) => handleInputChange('level', e.target.value as 'undergraduate' | 'postgraduate')}
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
            >
              <option value="undergraduate">Undergraduate</option>
              <option value="postgraduate">Postgraduate</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              <Users className="w-4 h-4 inline mr-2" />
              Semester
            </label>
            <select
              value={formData.semester}
              onChange={(e) => handleInputChange('semester', parseInt(e.target.value))}
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
            >
              {[1, 2, 3, 4, 5, 6, 7, 8].map(sem => (
                <option key={sem} value={sem}>Semester {sem}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              Course Type
            </label>
            <select
              value={formData.courseType}
              onChange={(e) => handleInputChange('courseType', e.target.value as 'theory' | 'practical' | 'theory-practical')}
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
            >
              <option value="theory">Theory</option>
              <option value="practical">Practical</option>
              <option value="theory-practical">Theory + Practical</option>
            </select>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              <Clock className="w-4 h-4 inline mr-2" />
              Duration (weeks)
            </label>
            <input
              type="number"
              value={formData.duration}
              onChange={(e) => handleInputChange('duration', parseInt(e.target.value))}
              min="12"
              max="20"
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              Specialization (Optional)
            </label>
            <input
              type="text"
              value={formData.specialization}
              onChange={(e) => handleInputChange('specialization', e.target.value)}
              placeholder="e.g., Artificial Intelligence, Cybersecurity"
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 placeholder-gray-500 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
            />
          </div>
        </div>

        <div className="pt-6">
          <button
            type="submit"
            disabled={isLoading || !formData.subject || !formData.department}
            className="w-full bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-bold py-6 px-8 rounded-2xl hover:from-cyan-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-2xl hover:shadow-cyan-500/25 text-xl transform hover:scale-105 hover:-translate-y-1"
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-3">
                <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <Sparkles className="w-5 h-5 animate-pulse" />
                Generating Course...
              </div>
            ) : (
              <div className="flex items-center justify-center gap-3">
                <Sparkles className="w-5 h-5" />
                Generate Course Syllabus
              </div>
            )}
          </button>
        </div>
      </form>

      <div className="mt-8 p-6 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-2xl border border-cyan-500/20">
        <p className="text-sm text-cyan-200 leading-relaxed">
          <strong className="text-cyan-300">Note:</strong> This system generates AICTE-compliant course syllabi for Indian universities. 
          The generated content includes learning objectives, outcomes, assessment patterns, and resource recommendations 
          aligned with Indian academic standards.
        </p>
      </div>
    </div>
  );
};