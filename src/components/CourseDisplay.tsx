import React from 'react';
import {
  BookOpen,
  Target,
  Award,
  Clock,
  Users,
  FileText,
  ExternalLink,
  Download,
  Calendar,
  GraduationCap,
  Sparkles,
  TrendingUp,
  Briefcase,
  Zap,
  BarChart3,
  Star
} from 'lucide-react';
import { Course } from '../types/course';
import { QualityAssessment } from './QualityAssessment';

interface CourseDisplayProps {
  course: Course;
  onExportPDF: () => void;
  onStartNew: () => void;
}

export const CourseDisplay: React.FC<CourseDisplayProps> = ({ 
  course, 
  onExportPDF, 
  onStartNew 
}) => {
  const totalContactHours = course.contactHours.lecture + course.contactHours.tutorial + course.contactHours.practical;

  return (
    <div className="max-w-6xl mx-auto space-y-8" id="course-content">
      {/* Header */}
      <div className="bg-gradient-to-r from-cyan-600 via-blue-600 to-purple-600 text-white rounded-2xl p-8 shadow-2xl border border-cyan-500/20">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white to-cyan-100 bg-clip-text text-transparent">
              {course.title}
            </h1>
            <div className="flex items-center gap-6 text-cyan-100">
              <span className="flex items-center gap-2 bg-white/10 px-3 py-1 rounded-full">
                <FileText className="w-4 h-4" />
                {course.code}
              </span>
              <span className="flex items-center gap-2 bg-white/10 px-3 py-1 rounded-full">
                <Award className="w-4 h-4" />
                {course.credits} Credits
              </span>
              <span className="flex items-center gap-2 bg-white/10 px-3 py-1 rounded-full">
                <Clock className="w-4 h-4" />
                {totalContactHours} Hours
              </span>
            </div>
          </div>
          <div className="flex gap-3">
            <button
              onClick={onExportPDF}
              className="bg-white/20 hover:bg-white/30 px-6 py-3 rounded-xl transition-all duration-300 flex items-center gap-2 backdrop-blur-sm transform hover:scale-105"
            >
              <Download className="w-4 h-4" />
              Export PDF
            </button>
            <button
              onClick={onStartNew}
              className="bg-white text-blue-600 hover:bg-cyan-50 px-6 py-3 rounded-xl transition-all duration-300 flex items-center gap-2 transform hover:scale-105"
            >
              <Sparkles className="w-4 h-4" />
              New Course
            </button>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-6 mt-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="text-sm text-cyan-100 mb-2 font-medium">Contact Hours</div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Lecture:</span>
                <span className="font-semibold">{course.contactHours.lecture}h</span>
              </div>
              <div className="flex justify-between">
                <span>Tutorial:</span>
                <span className="font-semibold">{course.contactHours.tutorial}h</span>
              </div>
              <div className="flex justify-between">
                <span>Practical:</span>
                <span className="font-semibold">{course.contactHours.practical}h</span>
              </div>
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="text-sm text-cyan-100 mb-2 font-medium">Assessment Pattern</div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Internal:</span>
                <span className="font-semibold">{course.assessmentPattern.internalAssessment}%</span>
              </div>
              <div className="flex justify-between">
                <span>End Sem:</span>
                <span className="font-semibold">{course.assessmentPattern.endSemExam}%</span>
              </div>
              {course.assessmentPattern.practical && (
                <div className="flex justify-between">
                  <span>Practical:</span>
                  <span className="font-semibold">{course.assessmentPattern.practical}%</span>
                </div>
              )}
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="text-sm text-cyan-100 mb-2 font-medium">Generated</div>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Calendar className="w-3 h-3" />
                <span className="text-sm">{course.createdAt.toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-2">
                <GraduationCap className="w-3 h-3" />
                <span className="text-sm">{course.generatedBy}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Prerequisites */}
      {course.prerequisites.length > 0 && (
        <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
          <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
            <div className="bg-gradient-to-r from-indigo-500 to-purple-500 p-2 rounded-xl">
              <Users className="w-6 h-6 text-white" />
            </div>
            Prerequisites
          </h3>
          <div className="flex flex-wrap gap-3">
            {course.prerequisites.map((prereq, index) => (
              <span
                key={index}
                className="bg-gradient-to-r from-indigo-500/20 to-purple-500/20 text-indigo-300 px-4 py-2 rounded-full text-sm font-medium border border-indigo-500/30 backdrop-blur-sm"
              >
                {prereq}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Course Context & Industry Relevance */}
      <div className="grid md:grid-cols-2 gap-8">
        {/* Industry Relevance */}
        {course.industryRelevance && (
          <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
            <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-2 rounded-xl">
                <Briefcase className="w-6 h-6 text-white" />
              </div>
              Industry Relevance
            </h3>

            <div className="space-y-6">
              {/* Skills */}
              <div>
                <h4 className="font-semibold text-emerald-300 mb-3 flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Key Skills
                </h4>
                <div className="flex flex-wrap gap-2">
                  {course.industryRelevance.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="bg-emerald-500/20 text-emerald-300 px-3 py-1 rounded-lg text-sm border border-emerald-500/30"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              {/* Tools */}
              <div>
                <h4 className="font-semibold text-teal-300 mb-3 flex items-center gap-2">
                  <Target className="w-4 h-4" />
                  Essential Tools
                </h4>
                <div className="flex flex-wrap gap-2">
                  {course.industryRelevance.tools.map((tool, index) => (
                    <span
                      key={index}
                      className="bg-teal-500/20 text-teal-300 px-3 py-1 rounded-lg text-sm border border-teal-500/30"
                    >
                      {tool}
                    </span>
                  ))}
                </div>
              </div>

              {/* Career Opportunities */}
              <div>
                <h4 className="font-semibold text-cyan-300 mb-3 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Career Opportunities
                </h4>
                <div className="flex flex-wrap gap-2">
                  {course.industryRelevance.careerOpportunities.map((career, index) => (
                    <span
                      key={index}
                      className="bg-cyan-500/20 text-cyan-300 px-3 py-1 rounded-lg text-sm border border-cyan-500/30"
                    >
                      {career}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Course Metrics */}
        <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
          <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
            <div className="bg-gradient-to-r from-orange-500 to-red-500 p-2 rounded-xl">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            Course Metrics
          </h3>

          <div className="space-y-6">
            {/* Difficulty Level */}
            {course.difficultyLevel && (
              <div>
                <h4 className="font-semibold text-orange-300 mb-3 flex items-center gap-2">
                  <Star className="w-4 h-4" />
                  Difficulty Level
                </h4>
                <div className="flex items-center gap-3">
                  <div className="flex-1 bg-gray-700 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${(course.difficultyLevel / 10) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-orange-300 font-bold">{course.difficultyLevel}/10</span>
                </div>
              </div>
            )}

            {/* Market Demand */}
            {course.marketDemand && (
              <div>
                <h4 className="font-semibold text-red-300 mb-3 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Market Demand
                </h4>
                <span className={`px-4 py-2 rounded-full text-sm font-bold ${
                  course.marketDemand === 'high'
                    ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                    : course.marketDemand === 'medium'
                    ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
                    : 'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {course.marketDemand.toUpperCase()}
                </span>
              </div>
            )}

            {/* Certifications */}
            {course.industryRelevance?.certifications && course.industryRelevance.certifications.length > 0 && (
              <div>
                <h4 className="font-semibold text-purple-300 mb-3 flex items-center gap-2">
                  <Award className="w-4 h-4" />
                  Relevant Certifications
                </h4>
                <div className="space-y-2">
                  {course.industryRelevance.certifications.map((cert, index) => (
                    <div
                      key={index}
                      className="bg-purple-500/20 text-purple-300 px-3 py-2 rounded-lg text-sm border border-purple-500/30"
                    >
                      {cert}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Course Objectives */}
      <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
        <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
          <div className="bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-xl">
            <Target className="w-6 h-6 text-white" />
          </div>
          Course Objectives
        </h3>
        <div className="space-y-4">
          {course.objectives.map((objective) => (
            <div key={objective.id} className="flex gap-4 p-4 bg-gray-900/30 rounded-xl border border-gray-700/30">
              <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                {objective.id}
              </div>
              <p className="text-gray-300 leading-relaxed">{objective.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Course Outcomes */}
      <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
        <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
          <div className="bg-gradient-to-r from-purple-500 to-violet-500 p-2 rounded-xl">
            <Award className="w-6 h-6 text-white" />
          </div>
          Course Outcomes
        </h3>
        <div className="grid gap-4">
          {course.outcomes.map((outcome) => (
            <div key={outcome.id} className="border border-purple-500/30 rounded-xl p-6 hover:bg-purple-500/5 transition-all duration-300 bg-gray-900/20">
              <div className="flex justify-between items-start mb-3">
                <span className="bg-gradient-to-r from-purple-500 to-violet-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                  {outcome.id}
                </span>
                <span className="bg-gray-700/50 text-gray-300 px-3 py-1 rounded-full text-xs font-medium">
                  {outcome.bloomsLevel}
                </span>
              </div>
              <p className="text-gray-300 leading-relaxed">{outcome.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Course Units */}
      <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
        <h3 className="text-2xl font-semibold text-cyan-300 mb-8 flex items-center gap-3">
          <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-2 rounded-xl">
            <BookOpen className="w-6 h-6 text-white" />
          </div>
          Course Curriculum
        </h3>
        <div className="space-y-8">
          {course.units.map((unit) => (
            <div key={unit.unitNumber} className="border border-gray-700/50 rounded-2xl p-8 hover:shadow-xl transition-all duration-500 bg-gray-900/20 hover:bg-gray-900/30">
              <div className="flex justify-between items-start mb-6">
                <h4 className="text-xl font-bold text-cyan-300">
                  Unit {unit.unitNumber}: {unit.title}
                </h4>
                <span className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                  {unit.contactHours} hours
                </span>
              </div>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h5 className="font-semibold text-cyan-400 mb-4 flex items-center gap-2">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                    Topics Covered
                  </h5>
                  <ul className="space-y-2">
                    {unit.topics.map((topic, index) => (
                      <li key={index} className="text-gray-300 flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                        {topic}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h5 className="font-semibold text-purple-400 mb-4 flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    Learning Outcomes
                  </h5>
                  <ul className="space-y-2">
                    {unit.learningOutcomes.map((outcome, index) => (
                      <li key={index} className="text-gray-300 flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                        {outcome}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Resources Section */}
      <div className="grid md:grid-cols-2 gap-8">
        {/* Textbooks */}
        <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
          <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
            <div className="bg-gradient-to-r from-orange-500 to-amber-500 p-2 rounded-xl">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            Prescribed Textbooks
          </h3>
          <div className="space-y-6">
            {course.textBooks.map((book, index) => (
              <div key={index} className="border-l-4 border-orange-500 pl-6 py-2 bg-gray-900/20 rounded-r-xl">
                <h4 className="font-semibold text-orange-300 mb-2">{book.title}</h4>
                <p className="text-gray-400 text-sm mb-1">
                  {book.authors.join(', ')} • {book.publisher} ({book.year})
                </p>
                {book.isbn && (
                  <p className="text-gray-500 text-xs">ISBN: {book.isbn}</p>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Reference Books */}
        <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
          <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
            <div className="bg-gradient-to-r from-teal-500 to-cyan-500 p-2 rounded-xl">
              <FileText className="w-6 h-6 text-white" />
            </div>
            Reference Books
          </h3>
          <div className="space-y-6">
            {course.referenceBooks.map((book, index) => (
              <div key={index} className="border-l-4 border-teal-500 pl-6 py-2 bg-gray-900/20 rounded-r-xl">
                <h4 className="font-semibold text-teal-300 mb-2">{book.title}</h4>
                <p className="text-gray-400 text-sm">
                  {book.authors.join(', ')} • {book.publisher} ({book.year})
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Online Resources */}
      <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
        <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
          <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-2 rounded-xl">
            <ExternalLink className="w-6 h-6 text-white" />
          </div>
          Online Resources & Websites
        </h3>
        <div className="grid md:grid-cols-2 gap-4">
          {course.onlineResources.map((resource, index) => (
            <a
              key={index}
              href={resource}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-3 p-4 border border-gray-700/50 rounded-xl hover:bg-gray-900/30 transition-all duration-300 transform hover:scale-105 bg-gray-900/20"
            >
              <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-2 rounded-lg">
                <ExternalLink className="w-4 h-4 text-white" />
              </div>
              <span className="text-gray-300 truncate">{resource}</span>
            </a>
          ))}
        </div>
      </div>

      {/* Quality Assessment */}
      <QualityAssessment course={course} />

      {/* Footer */}
      <div className="bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-xl rounded-2xl p-8 text-center border border-gray-700/50">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Sparkles className="w-6 h-6 text-cyan-400" />
          <span className="text-lg font-semibold text-cyan-300">AI-Generated Course Syllabus</span>
        </div>
        <p className="text-gray-400 leading-relaxed">
          This course syllabus was generated using AI-powered curriculum design system with quality validation.
          <br />
          Please review and adapt according to your institution's specific requirements and AICTE guidelines.
        </p>
      </div>
    </div>
  );
};