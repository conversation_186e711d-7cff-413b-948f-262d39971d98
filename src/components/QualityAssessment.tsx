import React, { useState } from 'react';
import { 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  Lightbulb, 
  TrendingUp,
  FileText,
  Download,
  Eye,
  EyeOff
} from 'lucide-react';
import { Course } from '../types/course';
import { QualityValidator, ValidationResult } from '../services/qualityValidator';

interface QualityAssessmentProps {
  course: Course;
}

export const QualityAssessment: React.FC<QualityAssessmentProps> = ({ course }) => {
  const [validation, setValidation] = useState<ValidationResult>(() => 
    QualityValidator.validateCourse(course)
  );
  const [isExpanded, setIsExpanded] = useState(false);
  const [showReport, setShowReport] = useState(false);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreGradient = (score: number) => {
    if (score >= 80) return 'from-green-500 to-emerald-500';
    if (score >= 60) return 'from-yellow-500 to-orange-500';
    return 'from-red-500 to-pink-500';
  };

  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-400" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      case 'info': return <Info className="w-4 h-4 text-blue-400" />;
      default: return <Info className="w-4 h-4 text-gray-400" />;
    }
  };

  const downloadReport = () => {
    const report = QualityValidator.generateImprovementReport(course);
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${course.code}_quality_report.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-2xl font-semibold text-cyan-300 flex items-center gap-3">
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded-xl">
            <TrendingUp className="w-6 h-6 text-white" />
          </div>
          Quality Assessment
        </h3>
        
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowReport(!showReport)}
            className="flex items-center gap-2 px-4 py-2 bg-gray-700/50 text-gray-300 rounded-lg hover:bg-gray-600/50 transition-colors"
          >
            {showReport ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            {showReport ? 'Hide Report' : 'View Report'}
          </button>
          
          <button
            onClick={downloadReport}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            <Download className="w-4 h-4" />
            Download Report
          </button>
        </div>
      </div>

      {/* Quality Score */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <span className="text-lg font-medium text-gray-300">Overall Quality Score</span>
          <span className={`text-3xl font-bold ${getScoreColor(validation.score)}`}>
            {validation.score}/100
          </span>
        </div>
        
        <div className="w-full bg-gray-700 rounded-full h-4 mb-2">
          <div 
            className={`bg-gradient-to-r ${getScoreGradient(validation.score)} h-4 rounded-full transition-all duration-1000 ease-out`}
            style={{ width: `${validation.score}%` }}
          ></div>
        </div>
        
        <div className="flex justify-between text-sm text-gray-400">
          <span>Poor</span>
          <span>Good</span>
          <span>Excellent</span>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-green-500/20 border border-green-500/30 rounded-xl p-4 text-center">
          <div className="text-2xl font-bold text-green-400">{validation.strengths.length}</div>
          <div className="text-sm text-green-300">Strengths</div>
        </div>
        
        <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-xl p-4 text-center">
          <div className="text-2xl font-bold text-yellow-400">{validation.issues.length}</div>
          <div className="text-sm text-yellow-300">Issues</div>
        </div>
        
        <div className="bg-blue-500/20 border border-blue-500/30 rounded-xl p-4 text-center">
          <div className="text-2xl font-bold text-blue-400">{validation.suggestions.length}</div>
          <div className="text-sm text-blue-300">Suggestions</div>
        </div>
      </div>

      {/* Toggle Details */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-center gap-2 py-3 text-cyan-400 hover:text-cyan-300 transition-colors border-t border-gray-700/50"
      >
        {isExpanded ? 'Hide Details' : 'Show Details'}
        <div className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}>
          ▼
        </div>
      </button>

      {/* Detailed Results */}
      {isExpanded && (
        <div className="mt-6 space-y-6">
          {/* Strengths */}
          {validation.strengths.length > 0 && (
            <div>
              <h4 className="font-semibold text-green-300 mb-3 flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                Strengths
              </h4>
              <div className="space-y-2">
                {validation.strengths.map((strength, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                    <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                    <span className="text-green-200">{strength}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Issues */}
          {validation.issues.length > 0 && (
            <div>
              <h4 className="font-semibold text-yellow-300 mb-3 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Issues to Address
              </h4>
              <div className="space-y-2">
                {validation.issues.map((issue, index) => (
                  <div key={index} className={`flex items-start gap-3 p-3 rounded-lg border ${
                    issue.type === 'error' 
                      ? 'bg-red-500/10 border-red-500/20' 
                      : issue.type === 'warning'
                      ? 'bg-yellow-500/10 border-yellow-500/20'
                      : 'bg-blue-500/10 border-blue-500/20'
                  }`}>
                    {getIssueIcon(issue.type)}
                    <div className="flex-1">
                      <div className={`font-medium ${
                        issue.type === 'error' ? 'text-red-300' : 
                        issue.type === 'warning' ? 'text-yellow-300' : 'text-blue-300'
                      }`}>
                        {issue.category.toUpperCase()}
                      </div>
                      <div className="text-gray-300 text-sm">{issue.message}</div>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded ${
                      issue.severity === 'high' ? 'bg-red-600 text-white' :
                      issue.severity === 'medium' ? 'bg-yellow-600 text-white' :
                      'bg-gray-600 text-gray-200'
                    }`}>
                      {issue.severity}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Suggestions */}
          {validation.suggestions.length > 0 && (
            <div>
              <h4 className="font-semibold text-blue-300 mb-3 flex items-center gap-2">
                <Lightbulb className="w-5 h-5" />
                Improvement Suggestions
              </h4>
              <div className="space-y-3">
                {validation.suggestions.map((suggestion, index) => (
                  <div key={index} className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h5 className="font-medium text-blue-300">{suggestion.title}</h5>
                      <div className="flex gap-2">
                        <span className={`text-xs px-2 py-1 rounded ${
                          suggestion.impact === 'high' ? 'bg-red-600 text-white' :
                          suggestion.impact === 'medium' ? 'bg-yellow-600 text-white' :
                          'bg-green-600 text-white'
                        }`}>
                          {suggestion.impact} impact
                        </span>
                        <span className={`text-xs px-2 py-1 rounded ${
                          suggestion.effort === 'high' ? 'bg-red-600 text-white' :
                          suggestion.effort === 'medium' ? 'bg-yellow-600 text-white' :
                          'bg-green-600 text-white'
                        }`}>
                          {suggestion.effort} effort
                        </span>
                      </div>
                    </div>
                    <p className="text-gray-300 text-sm">{suggestion.description}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Full Report */}
      {showReport && (
        <div className="mt-6 p-6 bg-gray-900/50 rounded-xl border border-gray-600/30">
          <div className="flex items-center gap-2 mb-4">
            <FileText className="w-5 h-5 text-cyan-400" />
            <h4 className="font-semibold text-cyan-300">Detailed Quality Report</h4>
          </div>
          <pre className="text-sm text-gray-300 whitespace-pre-wrap font-mono overflow-x-auto">
            {QualityValidator.generateImprovementReport(course)}
          </pre>
        </div>
      )}
    </div>
  );
};
