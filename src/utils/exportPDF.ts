import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export const exportToPDF = async (elementId: string, filename: string) => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error('Element not found for PDF export');
    }

    // Temporarily modify styles for better PDF output
    const originalStyles = element.style.cssText;
    element.style.background = 'white';
    element.style.color = 'black';

    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      backgroundColor: '#ffffff',
      logging: false,
      width: element.scrollWidth,
      height: element.scrollHeight
    });

    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF('p', 'mm', 'a4');
    
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    
    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
    const imgX = (pdfWidth - imgWidth * ratio) / 2;
    const imgY = 0;

    // If content is longer than one page, split it
    const totalPDFPages = Math.ceil(imgHeight * ratio / pdfHeight);
    
    for (let i = 0; i < totalPDFPages; i++) {
      if (i > 0) {
        pdf.addPage();
      }
      
      const srcY = i * (pdfHeight / ratio);
      const srcHeight = Math.min(imgHeight - srcY, pdfHeight / ratio);
      
      if (srcHeight > 0) {
        pdf.addImage(
          imgData,
          'PNG',
          imgX,
          imgY,
          imgWidth * ratio,
          srcHeight * ratio,
          undefined,
          'FAST',
          0,
          -srcY * ratio
        );
      }
    }

    // Restore original styles
    element.style.cssText = originalStyles;

    pdf.save(filename);
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw new Error('Failed to export PDF. Please try again.');
  }
};